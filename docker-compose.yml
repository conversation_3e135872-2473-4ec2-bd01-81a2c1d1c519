services:

  fluxgym:
    build:
      context: .
      # change the dockerfile to Dockerfile.cuda12.4 if you are running CUDA 12.4 drivers otherwise leave as is
      dockerfile: Dockerfile
    image: fluxgym
    container_name: fluxgym
    ports:
      - 7860:7860
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - ./:/app/fluxgym
    stop_signal: SIGKILL
    tty: true
    deploy:
      resources:
        reservations:
          devices:
          - driver: nvidia
            count: all
            capabilities: [gpu]
    restart: unless-stopped